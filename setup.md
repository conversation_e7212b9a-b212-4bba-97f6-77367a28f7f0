# Setup Guide for S2T Audio Client

This guide will help you set up and run the React.js audio streaming application.

## Quick Test (No Installation Required)

If you want to quickly test the WebSocket audio streaming functionality without installing Node.js:

1. Open `test.html` in your web browser
2. Grant microphone permission when prompted
3. Select your microphone from the dropdown
4. Click "Connect" to establish WebSocket connection
5. Click "Start Streaming" to begin audio capture

**Note**: The test.html version uses simplified audio processing and may not have all the features of the full React application.

## Full Setup (Recommended)

### Step 1: Install Node.js

#### Windows:
1. Download Node.js from [https://nodejs.org/](https://nodejs.org/)
2. Download the LTS version (recommended)
3. Run the installer and follow the setup wizard
4. Restart your command prompt/terminal

#### macOS:
```bash
# Using Homebrew (recommended)
brew install node

# Or download from https://nodejs.org/
```

#### Linux (Ubuntu/Debian):
```bash
# Using NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# Or using snap
sudo snap install node --classic
```

### Step 2: Verify Installation

Open a terminal/command prompt and run:
```bash
node --version
npm --version
```

You should see version numbers for both commands.

### Step 3: Install Project Dependencies

Navigate to the project directory and run:
```bash
npm install
```

This will install all required dependencies including:
- React 18.2.0
- Vite 5.0.8
- ESLint and related plugins

### Step 4: Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## WebSocket Server Requirements

The application expects a WebSocket server running on `localhost:63889` with the following endpoints:

- **Info Endpoint**: `GET https://localhost:63889/api/v1/audio/websocket-info`
  - Should return JSON with WebSocket connection details
  - Example response: `{"url": "wss://localhost:63889/ws/audio-stream"}`

- **WebSocket Endpoint**: `wss://localhost:63889/ws/audio-stream`
  - Protocol: `audio-stream-v1`
  - Accepts JSON control messages: `{"type": "start"}`, `{"type": "stop"}`
  - Receives binary PCM audio data (16kHz, mono, 16-bit)

## Browser Requirements

### Supported Browsers:
- Chrome/Chromium 66+ (recommended)
- Firefox 60+
- Safari 11.1+
- Edge 79+

### Required Permissions:
- **Microphone Access**: Required for audio capture
- **HTTPS**: Required in production (localhost works with HTTP)

### Browser Settings:
1. Allow microphone access when prompted
2. Ensure microphone is not being used by other applications
3. Check browser's site settings if microphone access is blocked

## Troubleshooting

### Node.js Installation Issues:
- **Windows**: Make sure to restart command prompt after installation
- **macOS**: If using Homebrew, run `brew doctor` to check for issues
- **Linux**: Ensure you have sufficient permissions for installation

### Dependency Installation Issues:
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Development Server Issues:
```bash
# Check if port 3000 is already in use
netstat -an | grep 3000

# Use different port
npm run dev -- --port 3001
```

### WebSocket Connection Issues:
1. Verify WebSocket server is running on correct port
2. Check browser console for detailed error messages
3. Test with `test.html` first to isolate issues
4. Ensure firewall/antivirus is not blocking connections

### Microphone Access Issues:
1. Check browser permissions in settings
2. Ensure microphone is not used by other applications
3. Try different microphone if available
4. Restart browser and grant permissions again

## Production Build

To build for production:
```bash
npm run build
```

The built files will be in the `dist/` directory. Serve these files using a web server.

**Important**: Production deployment requires HTTPS for microphone access.

## Development Tips

### Hot Reload:
The Vite development server supports hot reload. Changes to React components will be reflected immediately without page refresh.

### Debugging:
- Open browser developer tools (F12)
- Check Console tab for JavaScript errors
- Check Network tab for WebSocket connection status
- Use React Developer Tools extension for component debugging

### Code Structure:
- `src/components/`: React components
- `src/utils/`: Utility functions for audio processing
- `src/config/`: Configuration files
- `public/`: Static assets

## Next Steps

1. Start with `test.html` to verify basic functionality
2. Install Node.js and dependencies for full React application
3. Ensure WebSocket server is running and accessible
4. Test microphone access and audio streaming
5. Monitor browser console for any errors or warnings

For additional help, check the main README.md file or browser developer console for detailed error messages.
