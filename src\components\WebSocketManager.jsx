import React, { useState, useRef, useCallback } from 'react';
import { WEBSOCKET_CONFIG } from '../config/websocket.js';
import { createAudioProcessor } from '../utils/audioUtils.js';

/**
 * WebSocket manager component for audio streaming
 * Handles WebSocket connection, audio capture, and streaming
 */
const WebSocketManager = ({ selectedDeviceId, onStatusUpdate }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  
  const wsRef = useRef(null);
  const audioProcessorRef = useRef(null);
  const streamRef = useRef(null);

  /**
   * Update status and notify parent component
   */
  const updateStatus = useCallback((status, message, type = 'info') => {
    setConnectionStatus(status);
    onStatusUpdate({ status, message, type, timestamp: new Date().toISOString() });
  }, [onStatusUpdate]);

  /**
   * Get WebSocket connection info from the API endpoint
   */
  const getWebSocketInfo = async () => {
    try {
      const response = await fetch(WEBSOCKET_CONFIG.infoEndpoint);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Failed to get WebSocket info:', error);
      // Fallback to default configuration
      return {
        url: 'wss://localhost:63889/ws/audio-stream',
        protocol: WEBSOCKET_CONFIG.protocol
      };
    }
  };

  /**
   * Establish WebSocket connection
   */
  const connectWebSocket = async () => {
    try {
      updateStatus('connecting', 'Getting WebSocket connection info...');
      
      const wsInfo = await getWebSocketInfo();
      const wsUrl = wsInfo.url || 'wss://localhost:63889/ws/audio-stream';
      
      updateStatus('connecting', `Connecting to ${wsUrl}...`);
      
      wsRef.current = new WebSocket(wsUrl, WEBSOCKET_CONFIG.protocol);
      
      wsRef.current.onopen = () => {
        setIsConnected(true);
        updateStatus('connected', 'WebSocket connected successfully', 'success');
      };
      
      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          updateStatus('connected', `Server: ${JSON.stringify(message)}`, 'info');
        } catch (error) {
          updateStatus('connected', `Server message: ${event.data}`, 'info');
        }
      };
      
      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        updateStatus('error', 'WebSocket connection error', 'error');
      };
      
      wsRef.current.onclose = (event) => {
        setIsConnected(false);
        setIsStreaming(false);
        updateStatus('disconnected', `Connection closed (${event.code}: ${event.reason})`, 'warning');
        
        // Clean up audio processing
        if (audioProcessorRef.current) {
          audioProcessorRef.current.stop();
          audioProcessorRef.current = null;
        }
        
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }
      };
      
    } catch (error) {
      console.error('Failed to connect:', error);
      updateStatus('error', `Connection failed: ${error.message}`, 'error');
    }
  };

  /**
   * Start audio streaming
   */
  const startStreaming = async () => {
    if (!isConnected || !selectedDeviceId) {
      updateStatus('error', 'WebSocket not connected or no device selected', 'error');
      return;
    }

    try {
      updateStatus('connected', 'Starting audio capture...', 'info');
      
      // Get audio stream from selected device
      const constraints = {
        audio: {
          deviceId: selectedDeviceId,
          sampleRate: WEBSOCKET_CONFIG.audio.sampleRate,
          channelCount: WEBSOCKET_CONFIG.audio.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      };
      
      streamRef.current = await navigator.mediaDevices.getUserMedia(constraints);
      
      // Send start message to server
      wsRef.current.send(JSON.stringify({ 
        type: 'start',
        config: WEBSOCKET_CONFIG.audio,
        session: WEBSOCKET_CONFIG.session
      }));
      
      // Create audio processor
      audioProcessorRef.current = createAudioProcessor(
        streamRef.current,
        (audioData) => {
          if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
            wsRef.current.send(audioData);
          }
        }
      );
      
      setIsStreaming(true);
      updateStatus('streaming', 'Audio streaming started', 'success');
      
    } catch (error) {
      console.error('Failed to start streaming:', error);
      updateStatus('error', `Failed to start streaming: ${error.message}`, 'error');
    }
  };

  /**
   * Stop audio streaming
   */
  const stopStreaming = () => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({ type: 'stop' }));
    }
    
    if (audioProcessorRef.current) {
      audioProcessorRef.current.stop();
      audioProcessorRef.current = null;
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    setIsStreaming(false);
    updateStatus('connected', 'Audio streaming stopped', 'info');
  };

  /**
   * Disconnect WebSocket
   */
  const disconnect = () => {
    if (isStreaming) {
      stopStreaming();
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  };

  return (
    <div className="websocket-manager">
      <div className="button-group">
        {!isConnected ? (
          <button 
            onClick={connectWebSocket}
            disabled={!selectedDeviceId}
          >
            Connect
          </button>
        ) : (
          <>
            {!isStreaming ? (
              <button onClick={startStreaming}>
                Start Streaming
              </button>
            ) : (
              <button onClick={stopStreaming}>
                Stop Streaming
              </button>
            )}
            <button onClick={disconnect}>
              Disconnect
            </button>
          </>
        )}
      </div>
      
      <div className="connection-info">
        <p>Status: <strong>{connectionStatus}</strong></p>
        {isStreaming && (
          <p style={{ color: '#4ade80' }}>
            🎤 Streaming audio at {WEBSOCKET_CONFIG.audio.sampleRate}Hz, {WEBSOCKET_CONFIG.audio.channels} channel(s)
          </p>
        )}
      </div>
    </div>
  );
};

export default WebSocketManager;
