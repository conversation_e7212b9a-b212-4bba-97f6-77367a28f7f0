# S2T Audio Client

A React.js Single Page Application (SPA) that streams live audio to a WebSocket server using Vite as the build tool.

## Features

- **Device Selection**: Automatically detects and lists available microphone input devices
- **WebSocket Streaming**: Establishes connection to audio streaming WebSocket server
- **Real-time Audio Processing**: Captures audio, converts to 16kHz mono PCM, and streams in 100ms chunks
- **Status Monitoring**: Displays connection status and server messages in real-time
- **Modern UI**: Clean, responsive interface with dark/light theme support

## Configuration

The application uses the following WebSocket configuration:

```javascript
const WEBSOCKET_CONFIG = {
  infoEndpoint: 'https://localhost:63889/api/v1/audio/websocket-info',
  protocol: 'audio-stream-v1',
  audio: {
    sampleRate: 16000,        // 16 kHz
    bitsPerSample: 16,        // 16-bit
    channels: 1,              // Mono
    chunkIntervalMs: 100,     // Send chunks every 100ms
    format: 'PCM'             // Raw PCM data
  },
  session: {
    maxDurationMinutes: 120,
    clientInfo: 'React Audio Client v1.0'
  }
};
```

## Prerequisites

- Node.js (version 16 or higher)
- npm or yarn package manager
- A compatible WebSocket server running on `localhost:63889`

## Installation

### Step 1: Install Node.js
If Node.js is not installed on your system:

1. Download Node.js from [https://nodejs.org/](https://nodejs.org/)
2. Install the LTS version (recommended)
3. Verify installation by running:
```bash
node --version
npm --version
```

### Step 2: Install Dependencies
Navigate to the project directory and install dependencies:
```bash
npm install
# or
yarn install
```

## Development

Start the development server:

```bash
npm run dev
# or
yarn dev
```

The application will be available at `http://localhost:3000`

## Usage

1. **Grant Microphone Permission**: The browser will request microphone access when you first load the page
2. **Select Microphone**: Choose your desired audio input device from the dropdown
3. **Connect**: Click "Connect" to establish WebSocket connection
4. **Start Streaming**: Click "Start Streaming" to begin capturing and streaming audio
5. **Monitor Status**: View real-time connection status and server messages
6. **Stop/Disconnect**: Use the respective buttons to stop streaming or disconnect

## Audio Processing

The application performs the following audio processing:

- **Device Access**: Uses `navigator.mediaDevices.getUserMedia()` to access the selected microphone
- **Audio Context**: Creates Web Audio API context for real-time processing
- **Format Conversion**: Converts audio to 16kHz mono PCM format as required by the server
- **Chunking**: Sends audio data in 100ms chunks via WebSocket binary messages
- **Error Handling**: Includes comprehensive error handling for device access and connection issues

## WebSocket Protocol

The client sends the following message types:

- `{"type": "start"}` - Initiates audio streaming session
- `{"type": "stop"}` - Stops audio streaming session
- Binary audio data - Raw PCM audio chunks

The server responds with JSON status messages that are displayed in the UI.

## Browser Compatibility

- Chrome/Chromium (recommended)
- Firefox
- Safari (with some limitations)
- Edge

**Note**: Requires HTTPS in production for microphone access.

## Build for Production

```bash
npm run build
# or
yarn build
```

The built files will be in the `dist/` directory.

## Project Structure

```
src/
├── components/
│   ├── DeviceSelector.jsx    # Microphone device selection
│   └── WebSocketManager.jsx  # WebSocket connection management
├── config/
│   └── websocket.js          # WebSocket configuration
├── utils/
│   └── audioUtils.js         # Audio processing utilities
├── App.jsx                   # Main application component
├── main.jsx                  # Application entry point
└── index.css                 # Global styles
```

## Troubleshooting

### Microphone Access Issues
- Ensure you've granted microphone permission in your browser
- Check that your microphone is not being used by another application
- Try refreshing the page and granting permission again

### WebSocket Connection Issues
- Verify the WebSocket server is running on `localhost:63889`
- Check browser console for detailed error messages
- Ensure the server supports the `audio-stream-v1` protocol

### Audio Quality Issues
- Check your microphone settings in the operating system
- Ensure stable network connection for WebSocket streaming
- Monitor the status messages for any processing errors

## License

This project is created for demonstration purposes.
