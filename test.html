<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S2T Audio Client - Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #242424;
            color: white;
        }
        .container {
            text-align: center;
        }
        button {
            background-color: #646cff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #535bf2;
        }
        button:disabled {
            background-color: #666;
            cursor: not-allowed;
        }
        select {
            padding: 10px;
            margin: 10px;
            border-radius: 5px;
            background-color: #1a1a1a;
            color: white;
            border: 1px solid #646cff;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background-color: #1a1a1a;
            border: 1px solid #333;
            text-align: left;
        }
        .status.connected {
            border-color: #4ade80;
            background-color: rgba(74, 222, 128, 0.1);
        }
        .status.error {
            border-color: #ef4444;
            background-color: rgba(239, 68, 68, 0.1);
        }
        .config {
            background-color: #1a1a1a;
            padding: 15px;
            border-radius: 5px;
            text-align: left;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 S2T Audio Client - Test</h1>
        <p>Simple test version without React framework</p>
        
        <div>
            <label for="deviceSelect">Select Microphone:</label><br>
            <select id="deviceSelect">
                <option value="">Loading devices...</option>
            </select>
        </div>
        
        <div>
            <button id="connectBtn">Connect</button>
            <button id="startBtn" disabled>Start Streaming</button>
            <button id="stopBtn" disabled>Stop Streaming</button>
            <button id="disconnectBtn" disabled>Disconnect</button>
        </div>
        
        <div id="status" class="status">
            <h3>Status</h3>
            <div id="statusMessages">Ready to connect...</div>
        </div>
        
        <div class="config">
            <h4>Configuration</h4>
            <p><strong>WebSocket Endpoint:</strong> https://localhost:63889/api/v1/audio/websocket-info</p>
            <p><strong>Protocol:</strong> audio-stream-v1</p>
            <p><strong>Audio Format:</strong> PCM, 16000Hz, 1 channel, 16-bit</p>
            <p><strong>Chunk Interval:</strong> 100ms</p>
        </div>
    </div>

    <script>
        // WebSocket configuration
        const WEBSOCKET_CONFIG = {
            infoEndpoint: 'https://localhost:63889/api/v1/audio/websocket-info',
            protocol: 'audio-stream-v1',
            audio: {
                sampleRate: 16000,
                bitsPerSample: 16,
                channels: 1,
                chunkIntervalMs: 100,
                format: 'PCM'
            },
            session: {
                maxDurationMinutes: 120,
                clientInfo: 'React Audio Client v1.0'
            }
        };

        // Global variables
        let websocket = null;
        let audioContext = null;
        let mediaStream = null;
        let processor = null;
        let selectedDeviceId = '';

        // DOM elements
        const deviceSelect = document.getElementById('deviceSelect');
        const connectBtn = document.getElementById('connectBtn');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const statusDiv = document.getElementById('status');
        const statusMessages = document.getElementById('statusMessages');

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            enumerateDevices();
            setupEventListeners();
        });

        function setupEventListeners() {
            connectBtn.addEventListener('click', connectWebSocket);
            startBtn.addEventListener('click', startStreaming);
            stopBtn.addEventListener('click', stopStreaming);
            disconnectBtn.addEventListener('click', disconnect);
            deviceSelect.addEventListener('change', (e) => {
                selectedDeviceId = e.target.value;
            });
        }

        async function enumerateDevices() {
            try {
                // Request permission first
                await navigator.mediaDevices.getUserMedia({ audio: true });
                
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');
                
                deviceSelect.innerHTML = '';
                audioInputs.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.textContent = device.label || `Microphone ${device.deviceId.slice(0, 8)}...`;
                    deviceSelect.appendChild(option);
                });
                
                if (audioInputs.length > 0) {
                    selectedDeviceId = audioInputs[0].deviceId;
                    deviceSelect.value = selectedDeviceId;
                }
                
                updateStatus(`Found ${audioInputs.length} microphone(s)`, 'info');
            } catch (error) {
                updateStatus(`Error accessing microphones: ${error.message}`, 'error');
            }
        }

        async function connectWebSocket() {
            try {
                updateStatus('Connecting to WebSocket...', 'info');
                
                // Try to get WebSocket info, fallback to default
                let wsUrl = 'wss://localhost:63889/ws/audio-stream';
                try {
                    const response = await fetch(WEBSOCKET_CONFIG.infoEndpoint);
                    if (response.ok) {
                        const info = await response.json();
                        wsUrl = info.url || wsUrl;
                    }
                } catch (e) {
                    console.log('Using fallback WebSocket URL');
                }
                
                websocket = new WebSocket(wsUrl, WEBSOCKET_CONFIG.protocol);
                
                websocket.onopen = () => {
                    updateStatus('WebSocket connected successfully', 'success');
                    connectBtn.disabled = true;
                    startBtn.disabled = false;
                    disconnectBtn.disabled = false;
                };
                
                websocket.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        updateStatus(`Server: ${JSON.stringify(message)}`, 'info');
                    } catch (e) {
                        updateStatus(`Server: ${event.data}`, 'info');
                    }
                };
                
                websocket.onerror = (error) => {
                    updateStatus('WebSocket error occurred', 'error');
                };
                
                websocket.onclose = (event) => {
                    updateStatus(`Connection closed (${event.code}: ${event.reason})`, 'warning');
                    resetButtons();
                    if (mediaStream) {
                        mediaStream.getTracks().forEach(track => track.stop());
                    }
                };
                
            } catch (error) {
                updateStatus(`Connection failed: ${error.message}`, 'error');
            }
        }

        async function startStreaming() {
            if (!websocket || !selectedDeviceId) {
                updateStatus('WebSocket not connected or no device selected', 'error');
                return;
            }

            try {
                updateStatus('Starting audio capture...', 'info');
                
                const constraints = {
                    audio: {
                        deviceId: selectedDeviceId,
                        sampleRate: WEBSOCKET_CONFIG.audio.sampleRate,
                        channelCount: WEBSOCKET_CONFIG.audio.channels,
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                };
                
                mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
                
                // Send start message
                websocket.send(JSON.stringify({ 
                    type: 'start',
                    config: WEBSOCKET_CONFIG.audio,
                    session: WEBSOCKET_CONFIG.session
                }));
                
                // Setup audio processing (simplified version)
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const source = audioContext.createMediaStreamSource(mediaStream);
                
                // Note: This is a simplified version. The full React app has more sophisticated audio processing.
                updateStatus('Audio streaming started (simplified processing)', 'success');
                
                startBtn.disabled = true;
                stopBtn.disabled = false;
                
            } catch (error) {
                updateStatus(`Failed to start streaming: ${error.message}`, 'error');
            }
        }

        function stopStreaming() {
            if (websocket) {
                websocket.send(JSON.stringify({ type: 'stop' }));
            }
            
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                mediaStream = null;
            }
            
            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }
            
            updateStatus('Audio streaming stopped', 'info');
            startBtn.disabled = false;
            stopBtn.disabled = true;
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
            stopStreaming();
            resetButtons();
            updateStatus('Disconnected', 'info');
        }

        function resetButtons() {
            connectBtn.disabled = false;
            startBtn.disabled = true;
            stopBtn.disabled = true;
            disconnectBtn.disabled = true;
        }

        function updateStatus(message, type) {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ef4444' : 
                         type === 'success' ? '#4ade80' : 
                         type === 'warning' ? '#f59e0b' : '#fff';
            
            statusMessages.innerHTML += `<div style="margin-bottom: 0.5rem;">
                <span style="color: #888; font-size: 0.8em;">${timestamp}</span>
                <span style="color: ${color};"> ${message}</span>
            </div>`;
            
            statusMessages.scrollTop = statusMessages.scrollHeight;
            
            // Update status div class
            statusDiv.className = 'status';
            if (type === 'success' || message.includes('connected') || message.includes('streaming')) {
                statusDiv.className += ' connected';
            } else if (type === 'error') {
                statusDiv.className += ' error';
            }
        }
    </script>
</body>
</html>
