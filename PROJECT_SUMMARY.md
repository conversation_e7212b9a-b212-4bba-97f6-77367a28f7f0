# S2T Audio Client - Project Summary

## 🎯 Project Overview

A complete React.js Single Page Application (SPA) that streams live audio to a WebSocket server using Vite as the build tool. The application captures microphone audio, processes it to match server requirements (16kHz mono PCM), and streams it in real-time via WebSocket.

## 📁 Project Structure

```
S2TClient/
├── src/
│   ├── components/
│   │   ├── DeviceSelector.jsx      # Microphone device selection component
│   │   └── WebSocketManager.jsx    # WebSocket connection and streaming management
│   ├── config/
│   │   └── websocket.js            # WebSocket configuration constants
│   ├── utils/
│   │   └── audioUtils.js           # Audio processing utilities (PCM conversion, resampling)
│   ├── App.jsx                     # Main application component
│   ├── main.jsx                    # React application entry point
│   └── index.css                   # Global styles and UI theming
├── public/
│   └── vite.svg                    # Vite logo icon
├── package.json                    # Project dependencies and scripts
├── vite.config.js                  # Vite build configuration
├── index.html                      # HTML template
├── .eslintrc.cjs                   # ESLint configuration
├── test.html                       # Standalone test version (no Node.js required)
├── setup.bat                       # Windows setup script
├── start-dev.bat                   # Windows development server launcher
├── README.md                       # Comprehensive documentation
├── setup.md                        # Detailed setup instructions
└── PROJECT_SUMMARY.md              # This file
```

## 🚀 Key Features Implemented

### 1. Device Management
- **Automatic Device Enumeration**: Detects all available microphone input devices
- **Device Selection**: User-friendly dropdown to select preferred microphone
- **Permission Handling**: Graceful handling of microphone permission requests
- **Device Labeling**: Shows device names or fallback identifiers

### 2. WebSocket Communication
- **Dynamic Connection**: Fetches WebSocket info from API endpoint with fallback
- **Protocol Support**: Implements `audio-stream-v1` protocol
- **Message Handling**: Sends JSON control messages (`start`, `stop`)
- **Status Monitoring**: Real-time display of connection status and server messages
- **Error Recovery**: Comprehensive error handling and connection management

### 3. Audio Processing
- **Real-time Capture**: Uses Web Audio API for low-latency audio processing
- **Format Conversion**: Converts audio to required format (16kHz, mono, 16-bit PCM)
- **Sample Rate Conversion**: Resamples audio using linear interpolation
- **Stereo to Mono**: Converts stereo input to mono by averaging channels
- **Chunked Streaming**: Sends audio data in 100ms chunks as binary WebSocket messages

### 4. User Interface
- **Modern Design**: Clean, responsive UI with dark/light theme support
- **Real-time Status**: Live connection status and message display
- **Configuration Display**: Shows current WebSocket and audio settings
- **Intuitive Controls**: Clear start/stop/connect/disconnect buttons
- **Error Feedback**: User-friendly error messages and troubleshooting hints

## 🔧 Technical Implementation

### WebSocket Configuration
```javascript
const WEBSOCKET_CONFIG = {
  infoEndpoint: 'https://localhost:63889/api/v1/audio/websocket-info',
  protocol: 'audio-stream-v1',
  audio: {
    sampleRate: 16000,        // 16 kHz
    bitsPerSample: 16,        // 16-bit
    channels: 1,              // Mono
    chunkIntervalMs: 100,     // 100ms chunks
    format: 'PCM'             // Raw PCM data
  },
  session: {
    maxDurationMinutes: 120,
    clientInfo: 'React Audio Client v1.0'
  }
};
```

### Audio Processing Pipeline
1. **Capture**: `getUserMedia()` with device constraints
2. **Processing**: Web Audio API ScriptProcessorNode
3. **Conversion**: Float32 → 16-bit PCM conversion
4. **Resampling**: Linear interpolation to 16kHz
5. **Streaming**: Binary WebSocket transmission

### Component Architecture
- **DeviceSelector**: Handles microphone enumeration and selection
- **WebSocketManager**: Manages connection lifecycle and audio streaming
- **App**: Main component coordinating all functionality
- **audioUtils**: Pure functions for audio processing

## 🛠 Setup Options

### Option 1: Quick Test (No Installation)
- Open `test.html` in any modern browser
- Simplified version for immediate testing
- Basic WebSocket connectivity and audio capture

### Option 2: Full React Application
1. Install Node.js (16+)
2. Run `setup.bat` (Windows) or `npm install`
3. Start with `start-dev.bat` or `npm run dev`
4. Access at `http://localhost:3000`

## 📋 Requirements Met

✅ **WebSocket Configuration**: Exact configuration object as specified  
✅ **Device Selection**: Lists and allows selection of microphone devices  
✅ **Start/Stop Controls**: Proper WebSocket message handling  
✅ **Audio Streaming**: Real-time PCM audio streaming in 100ms chunks  
✅ **Status Display**: Connection status and server message display  
✅ **Vite Build Tool**: Complete Vite configuration and setup  
✅ **Local Execution**: Ready to run with `npm run dev`  
✅ **Functional Components**: Modern React hooks-based architecture  
✅ **Error Handling**: Comprehensive error handling and user feedback  
✅ **Minimal UI**: Clean, functional interface with all required controls  

## 🔍 Browser Compatibility

- **Chrome/Chromium 66+** (recommended)
- **Firefox 60+**
- **Safari 11.1+**
- **Edge 79+**

**Note**: Requires HTTPS in production for microphone access (localhost works with HTTP).

## 🎵 Audio Specifications

- **Input**: Any sample rate, mono/stereo
- **Output**: 16kHz, mono, 16-bit PCM
- **Latency**: ~100ms chunks for real-time streaming
- **Processing**: Web Audio API with fallback support

## 🚦 Getting Started

1. **Quick Test**: Open `test.html` in browser
2. **Full Setup**: Run `setup.bat` (Windows) or follow README.md
3. **Start Server**: Run `start-dev.bat` or `npm run dev`
4. **Grant Permissions**: Allow microphone access when prompted
5. **Select Device**: Choose microphone from dropdown
6. **Connect & Stream**: Use buttons to connect and start streaming

## 📞 WebSocket Server Requirements

The application expects a WebSocket server on `localhost:63889` with:
- Info endpoint: `GET /api/v1/audio/websocket-info`
- WebSocket endpoint: `wss://localhost:63889/ws/audio-stream`
- Protocol: `audio-stream-v1`
- Message format: JSON control messages, binary audio data

## 🎯 Success Criteria

This implementation provides a complete, production-ready React.js SPA that:
- Streams live audio via WebSocket using the exact specified configuration
- Works locally with Vite development server
- Requires no backend modifications
- Includes comprehensive error handling and user feedback
- Supports all modern browsers with proper fallbacks
- Provides both quick-test and full-development options
