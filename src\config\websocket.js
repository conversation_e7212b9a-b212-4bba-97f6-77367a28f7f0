// WebSocket connection configuration
export const WEBSOCKET_CONFIG = {
  // Get this from your API endpoint
  infoEndpoint: 'https://localhost:63889/api/v1/audio/websocket-info',

  // Connection will be: wss://localhost:63889/ws/audio-stream
  protocol: 'audio-stream-v1',
  
  // Audio configuration (matches your C# service)
  audio: {
    sampleRate: 16000,        // 16 kHz (recommended)
    bitsPerSample: 16,        // 16-bit
    channels: 1,              // Mono
    chunkIntervalMs: 100,     // Send chunks every 100ms
    format: 'PCM'             // Raw PCM data
  },
  
  // Session configuration
  session: {
    maxDurationMinutes: 120,
    clientInfo: 'React Audio Client v1.0'
  }
};
